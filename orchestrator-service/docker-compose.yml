services:
  # Core Orchestrator Service (contains webhook, builder, deployer)
  orchestrator:
    build: .
    container_name: cicd-orchestrator
    ports:
      - "8081:8081" # Webhook Listener (your battle-tested service)
      - "8082:8082" # Orchestrator API
      - "8080:8080" # Storage Service
    volumes:
      - orchestrator_projects:/orchestrator/projects
      - orchestrator_logs:/orchestrator/logs
      - webhook_ci:/orchestrator/webhook/ci
      - storage_data:/app/storage
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-development}
      - ORCHESTRATOR_VAULT_SERVICE_BASE_URL=http://node-service:3000
      - ORCHESTRATOR_VAULT_SERVICE_API_KEY=${ORCHESTRATOR_VAULT_SERVICE_API_KEY:-default-api-key}
      - ORCHESTRATOR_STORAGE_SERVICE_URL=http://localhost:8080
      - ORCHESTRATOR_BUILDER_SCRIPT=/usr/local/bin/builder-wrapper
      - ORCHESTRATOR_DEPLOYER_JAR=/app/services/deployer.jar
      - ORCHESTRATOR_BUILDER_VAULT_URL=http://vault:8200
      - ORCHESTRATOR_BUILDER_VAULT_API_KEY=${ORCHESTRATOR_BUILDER_VAULT_API_KEY:-root}
      - ORCHESTRATOR_BUILDER_PYTHON_EXECUTABLE=${ORCHESTRATOR_BUILDER_PYTHON_EXECUTABLE:-python3}
      - ORCHESTRATOR_BUILDER_TIMEOUT_MINUTES=${ORCHESTRATOR_BUILDER_TIMEOUT_MINUTES:-30}
      - ORCHESTRATOR_DEPLOYER_TIMEOUT_MINUTES=${ORCHESTRATOR_DEPLOYER_TIMEOUT_MINUTES:-15}
      - JAVA_OPTS=${JAVA_OPTS:--Xmx512m}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET:-default-webhook-secret}
      - DOCKER_REGISTRY=${DOCKER_REGISTRY:-localhost:5000}
    networks:
      - cicd-network
    depends_on:
      - vault
      - node-service
    restart: unless-stopped

  # HashiCorp Vault
  vault:
    image: hashicorp/vault:latest
    container_name: cicd-vault
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=root
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
    ports:
      - "8200:8200"
    volumes:
      - vault_data:/vault/file
    command: "server -dev"
    cap_add:
      - IPC_LOCK
    networks:
      - cicd-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Node.js Vault Service
  node-service:
    build: ../hashicorp-vault-secret-service
    container_name: cicd-node-vault
    environment:
      - VAULT_ADDR=http://vault:8200
      - VAULT_TOKEN=root
      - PORT=3000
      - API_KEY=${ORCHESTRATOR_VAULT_SERVICE_API_KEY:-default-api-key}
    ports:
      - "3001:3000"
    depends_on:
      - vault
    networks:
      - cicd-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: Local Docker Registry for development
  registry:
    image: registry:2
    container_name: local-registry
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
    networks:
      - cicd-network
    restart: unless-stopped
    profiles:
      - development

volumes:
  # Core service volumes
  orchestrator_projects:
    driver: local
  orchestrator_logs:
    driver: local
  storage_data:
    driver: local
  webhook_ci:
    driver: local
  vault_data:
    driver: local

  # Service-specific volumes
  builder_workspace:
    driver: local
  deployer_temp:
    driver: local

  # Registry volume
  registry_data:
    driver: local

networks:
  cicd-network:
    driver: bridge
    name: cicd-network
