server:
  port: 8082

spring:
  application:
    name: orchestrator-service
  profiles:
    active: development
  
  # No embedded database - using external storage service

# Orchestrator Configuration
orchestrator:
  directories:
    base: /orchestrator
    webhook: /orchestrator/webhook
    projects: /orchestrator/projects
    storage: /orchestrator/storage
    logs: /orchestrator/logs
    temp: /orchestrator/temp
  
  vault-service:
    base-url: http://node-service:3000
    api-key: 1234567890
    timeout-seconds: 30
  
  services:
    enable-webhook: false  # Disabled - using external webhook-listner service
    enable-builder: true
    enable-deployer: true
    enable-file-watcher: true
    enable-sequential-processing: true

  # External Services Configuration (for multi-service container)
  external-services:
    storage-service:
      url: ${ORCHESTRATOR_STORAGE_SERVICE_URL:http://storage-service:8080}
      enabled: true
    webhook-service:
      url: ${ORCHESTRATOR_WEBHOOK_SERVICE_URL:http://webhook-listener:8083}
      enabled: true
  
  builder:
    python-executable: python3
    builder-script: ${ORCHESTRATOR_BUILDER_SCRIPT:/app/services/builder/main.py}
    timeout-minutes: 30
    vault-url: http://node-service:3000/api/secrets/
    vault-api-key: 1234567890
    workspace-dir: /orchestrator/builder/workspace

  deployer:
    jar-path: ${ORCHESTRATOR_DEPLOYER_JAR:/app/services/deployer.jar}
    timeout-minutes: 20
    temp-dir: /orchestrator/deployer/temp

  # Pipeline Configuration
  pipeline:
    max-concurrent-builds: 3
    max-concurrent-deployments: 2
    build-queue-size: 10
    deployment-queue-size: 5
    enable-parallel-processing: true
    retry-attempts: 3
    retry-delay-seconds: 30
    enable-rollback: true
    rollback-timeout-minutes: 10

  # Security Configuration
  security:
    enable-webhook-signature-validation: true
    webhook-secret: ${WEBHOOK_SECRET:}
    enable-api-key-authentication: false
    api-key: ${API_KEY:}
    enable-rate-limiting: true
    rate-limit-requests-per-minute: 60
    enable-cors: true
    allowed-origins:
      - "http://localhost:3001"
      - "http://localhost:8080"
      - "http://localhost:8082"

  # API Documentation Configuration
  api:
    info:
      title: "CI/CD Orchestrator Service API"
      description: "Complete CI/CD orchestrator with embedded services for automated build and deployment pipelines"
      version: "1.0.0"
      contact:
        name: "CI/CD Team"
        email: "<EMAIL>"
        url: "https://github.com/company/orchestrator-service"
      license:
        name: "MIT License"
        url: "https://opensource.org/licenses/MIT"
    servers:
      - url: "http://localhost:8082"
        description: "Local Development Server"
      - url: "http://orchestrator-service:8082"
        description: "Docker Container Server"

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operationsSorter: method
    tagsSorter: alpha
    tryItOutEnabled: true
    filter: true
    displayRequestDuration: true
  show-actuator: true
  group-configs:
    - group: 'orchestrator'
      display-name: 'Orchestrator API'
      paths-to-match: '/orchestrator/**'
    - group: 'webhook'
      display-name: 'Webhook API'
      paths-to-match: '/webhook/**'

# Logging Configuration
logging:
  level:
    com.cicd.orchestrator: INFO
    org.springframework: WARN
    io.methvin.watcher: WARN
    "[io.methvin.watcher.DirectoryWatcher]": ERROR
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /orchestrator/logs/orchestrator.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      enabled: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: development

  datasource:
    url: *********************************

orchestrator:
  directories:
    base: ./orchestrator-dev
    webhook: ./orchestrator-dev/webhook
    projects: ./orchestrator-dev/projects
    storage: ./orchestrator-dev/storage
    logs: ./orchestrator-dev/logs
    temp: ./orchestrator-dev/temp
  
  vault-service:
    base-url: http://localhost:3001
  
  builder:
    vault-url: http://localhost:3001/api/secrets/
  
  deployer:
    jar-path: ./deployer.jar

  # Development-specific pipeline settings
  pipeline:
    max-concurrent-builds: 2
    max-concurrent-deployments: 1
    enable-parallel-processing: false

  # Development-specific security settings
  security:
    enable-webhook-signature-validation: false
    enable-rate-limiting: false

  # Development API configuration
  api:
    servers:
      - url: "http://localhost:8082"
        description: "Local Development Server"

logging:
  level:
    com.cicd.orchestrator: DEBUG

---
# Docker Profile
spring:
  config:
    activate:
      on-profile: docker

orchestrator:
  directories:
    base: /orchestrator
    webhook: /orchestrator/webhook
    projects: /orchestrator/projects
    storage: /orchestrator/storage
    logs: /orchestrator/logs
    temp: /orchestrator/temp
  
  vault-service:
    base-url: http://vault-service:3001

  # Docker-specific API configuration
  api:
    servers:
      - url: "http://orchestrator-service:8082"
        description: "Docker Container Server"
      - url: "http://localhost:8082"
        description: "Local Development Server"

---
# Production Profile
spring:
  config:
    activate:
      on-profile: production

orchestrator:
  vault-service:
    timeout-seconds: 60
  
  builder:
    timeout-minutes: 45
  
  deployer:
    timeout-minutes: 30

  # Production-specific pipeline settings
  pipeline:
    max-concurrent-builds: 5
    max-concurrent-deployments: 3
    build-queue-size: 20
    deployment-queue-size: 10
    retry-attempts: 5
    retry-delay-seconds: 60

  # Production-specific security settings
  security:
    enable-webhook-signature-validation: true
    enable-api-key-authentication: true
    enable-rate-limiting: true
    rate-limit-requests-per-minute: 100

logging:
  level:
    com.cicd.orchestrator: INFO
    org.springframework: WARN
