# CI/CD Orchestrator Deployment Guide

## 🚀 Quick Deployment on Any Machine

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- Git
- 4GB+ RAM
- 10GB+ disk space

### 1. <PERSON><PERSON> and Deploy
```bash
git clone <your-repo-url>
cd orchestrator-service
./deploy.sh
```

### 2. Configure Environment (Optional)
```bash
cp .env.example .env
# Edit .env with your specific values
nano .env
```

### 3. Verify Deployment
```bash
# Check all services are running
docker ps | grep cicd

# Test webhook endpoint
curl -X POST http://localhost:8081/ -H "Content-Type: application/json" -d '{"test": "webhook"}'

# Check logs
docker logs cicd-orchestrator -f
```

## 🔧 Service Endpoints

| Service | Port | URL | Purpose |
|---------|------|-----|---------|
| Webhook | 8081 | http://localhost:8081 | GitHub webhook receiver |
| Storage | 8080 | http://localhost:8080 | Build data storage |
| Orchestrator | 8082 | http://localhost:8082 | Main orchestrator API |
| Vault | 8200 | http://localhost:8200 | HashiCorp Vault |
| Node Vault | 3001 | http://localhost:3001 | Vault API service |

## 📋 Management Commands

### Start/Stop Services
```bash
# Start all services
docker compose up -d

# Stop all services
docker compose down

# Restart specific service
docker compose restart orchestrator
```

### View Logs
```bash
# All orchestrator logs
docker logs cicd-orchestrator -f

# Storage service logs
docker logs cicd-orchestrator | grep storage-service

# Webhook logs
docker logs cicd-orchestrator | grep webhook
```

### Backup Data
```bash
# Backup volumes
docker run --rm -v orchestrator_projects:/data -v $(pwd):/backup alpine tar czf /backup/projects-backup.tar.gz -C /data .
docker run --rm -v storage_data:/data -v $(pwd):/backup alpine tar czf /backup/storage-backup.tar.gz -C /data .
```

### Restore Data
```bash
# Restore volumes
docker run --rm -v orchestrator_projects:/data -v $(pwd):/backup alpine tar xzf /backup/projects-backup.tar.gz -C /data
docker run --rm -v storage_data:/data -v $(pwd):/backup alpine tar xzf /backup/storage-backup.tar.gz -C /data
```

## 🔍 Troubleshooting

### Common Issues

1. **Port 8081 not accessible**
   ```bash
   # Check if container is running
   docker ps | grep cicd-orchestrator
   
   # Check port mapping
   docker port cicd-orchestrator
   
   # Restart if needed
   docker compose restart orchestrator
   ```

2. **Permission errors**
   ```bash
   # Fix log directory permissions
   sudo mkdir -p /var/log/ci-cd
   sudo chown -R $USER:$USER /var/log/ci-cd
   sudo chmod -R 755 /var/log/ci-cd
   ```

3. **Services not starting**
   ```bash
   # Check logs for errors
   docker logs cicd-orchestrator --tail=50
   
   # Rebuild if needed
   docker compose build orchestrator
   docker compose up -d
   ```

## 🌐 GitHub Webhook Setup

1. Go to your GitHub repository settings
2. Navigate to Webhooks
3. Add webhook with URL: `http://your-server:8081/`
4. Set Content-Type: `application/json`
5. Select events: `push`, `pull_request`
6. Add webhook secret (optional but recommended)

## 🔐 Security Considerations

1. **Change default secrets** in `.env` file
2. **Use HTTPS** in production with reverse proxy
3. **Configure firewall** to restrict access
4. **Regular backups** of data volumes
5. **Monitor logs** for suspicious activity

## 📊 Monitoring

### Health Checks
```bash
# Check all services health
curl http://localhost:8081/health || echo "Webhook service down"
curl http://localhost:8080/health || echo "Storage service down"
curl http://localhost:8082/health || echo "Orchestrator service down"
```

### Performance Monitoring
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Volume usage
docker volume ls
```

## 🔄 Updates

### Update to Latest Version
```bash
git pull origin main
docker compose build orchestrator
docker compose up -d
```

### Rollback
```bash
# Stop current version
docker compose down

# Checkout previous version
git checkout <previous-commit>

# Deploy previous version
./deploy.sh
```

## 📞 Support

For issues and support:
1. Check logs: `docker logs cicd-orchestrator -f`
2. Verify configuration: `cat .env`
3. Test connectivity: `curl http://localhost:8081/`
4. Review this guide for troubleshooting steps
