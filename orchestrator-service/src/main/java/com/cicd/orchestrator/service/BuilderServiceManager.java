package com.cicd.orchestrator.service;

import com.cicd.orchestrator.config.OrchestratorConfig;
// Removed ConfigBuild import - using storage service client
import org.apache.commons.exec.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
public class BuilderServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(BuilderServiceManager.class);

    @Autowired
    private OrchestratorConfig config;

    @Autowired
    private VaultService vaultService;

    // No storage service dependency - builder manager is stateless

    /**
     * \
     * Execute builder service for a given project path and build ID
     */
    public CompletableFuture<Boolean> executeBuild(String projectPath, String buildId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Starting build execution for build ID: {} at path: {}", buildId, projectPath);

                // Build status updates handled by PipelineOrchestrator
                logger.info("Starting build for build ID: {}", buildId);

                // Verify project path exists
                if (!Files.exists(Paths.get(projectPath))) {
                    logger.error("Project path does not exist: {}", projectPath);
                    return false;
                }

                // Execute Python builder
                boolean buildSuccess = executePythonBuilder(projectPath, buildId);

                if (buildSuccess) {
                    logger.info("Build completed successfully for build ID: {}", buildId);
                    return true;
                } else {
                    logger.error("Build failed for build ID: {}", buildId);
                    return false;
                }

            } catch (Exception e) {
                logger.error("Error executing build for build ID: " + buildId, e);
                return false;
            }
        });
    }

    /**
     * Execute the Python builder service
     */
    private boolean executePythonBuilder(String projectPath, String buildId) {
        try {
            logger.info("Executing Python builder for build ID: {} at path: {}", buildId, projectPath);

            // Get environment variables from Vault
            Map<String, String> envVars = vaultService.getBuilderEnvironmentVariables();
            if (envVars.isEmpty()) {
                logger.error("Failed to get environment variables from Vault");
                return false;
            }

            // Prepare command
            CommandLine cmdLine = new CommandLine(config.getBuilder().getPythonExecutable());
            cmdLine.addArgument(config.getBuilder().getBuilderScript());
            cmdLine.addArgument(projectPath);
            cmdLine.addArgument("--build_id");
            cmdLine.addArgument(buildId);

            // Set up execution environment
            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0); // Success exit code

            // Set working directory to the project path
            executor.setWorkingDirectory(new File(projectPath));

            // Set up output streams
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
            executor.setStreamHandler(streamHandler);

            // Set timeout
            ExecuteWatchdog watchdog = new ExecuteWatchdog(config.getBuilder().getTimeoutMinutes() * 60 * 1000L);
            executor.setWatchdog(watchdog);

            // Prepare environment variables
            Map<String, String> environment = new HashMap<>(System.getenv());
            environment.putAll(envVars);

            // Execute command
            logger.info("Executing command: {}", cmdLine.toString());
            int exitCode = executor.execute(cmdLine, environment);

            // Log output
            String output = outputStream.toString();
            String error = errorStream.toString();

            if (!output.isEmpty()) {
                logger.info("Builder output for {}: {}", buildId, output);
            }
            if (!error.isEmpty()) {
                logger.warn("Builder error output for {}: {}", buildId, error);
            }

            if (exitCode == 0) {
                logger.info("Python builder completed successfully for build ID: {}", buildId);

                // Check if generated compose files exist
                if (checkGeneratedComposeFiles(projectPath, buildId)) {
                    return true;
                } else {
                    logger.error("Builder completed but no compose files were generated for build ID: {}", buildId);
                    return false;
                }
            } else {
                logger.error("Python builder failed with exit code: {} for build ID: {}", exitCode, buildId);
                return false;
            }

        } catch (IOException e) {
            logger.error("Failed to execute Python builder for build ID: " + buildId, e);
            return false;
        } catch (Exception e) {
            logger.error("Unexpected error during build execution for build ID: " + buildId, e);
            return false;
        }
    }

    /**
     * Check if generated compose files exist in the project directory
     */
    private boolean checkGeneratedComposeFiles(String projectPath, String buildId) {
        try {
            Path generatedComposeDir = Paths.get(projectPath, "generatedComposeFiles");

            if (!Files.exists(generatedComposeDir)) {
                logger.warn("Generated compose files directory does not exist: {}", generatedComposeDir);
                return false;
            }

            // Check for any compose files
            boolean hasComposeFiles = Files.list(generatedComposeDir)
                    .anyMatch(path -> path.getFileName().toString().endsWith(".yml") ||
                            path.getFileName().toString().endsWith(".yaml"));

            if (hasComposeFiles) {
                logger.info("Generated compose files found for build ID: {}", buildId);
                return true;
            } else {
                logger.warn("No compose files found in generated directory for build ID: {}", buildId);
                return false;
            }

        } catch (IOException e) {
            logger.error("Error checking generated compose files for build ID: " + buildId, e);
            return false;
        }
    }

    /**
     * Check if builder service is available
     */
    public boolean isBuilderAvailable() {
        try {
            // Check if Python executable exists
            CommandLine cmdLine = new CommandLine(config.getBuilder().getPythonExecutable());
            cmdLine.addArgument("--version");

            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream);
            executor.setStreamHandler(streamHandler);

            ExecuteWatchdog watchdog = new ExecuteWatchdog(5000); // 5 seconds timeout
            executor.setWatchdog(watchdog);

            int exitCode = executor.execute(cmdLine);

            if (exitCode == 0) {
                logger.debug("Python executable available: {}", outputStream.toString().trim());
                return true;
            }

            return false;

        } catch (Exception e) {
            logger.debug("Builder availability check failed", e);
            return false;
        }
    }

    /**
     * Get builder service status
     */
    public BuilderStatus getBuilderStatus() {
        boolean available = isBuilderAvailable();
        boolean vaultConnected = vaultService.testConnection();

        return new BuilderStatus(
                available,
                vaultConnected,
                config.getBuilder().getPythonExecutable(),
                config.getBuilder().getBuilderScript(),
                config.getBuilder().getTimeoutMinutes());
    }

    public static class BuilderStatus {
        private final boolean available;
        private final boolean vaultConnected;
        private final String pythonExecutable;
        private final String builderScript;
        private final int timeoutMinutes;

        public BuilderStatus(boolean available, boolean vaultConnected, String pythonExecutable,
                String builderScript, int timeoutMinutes) {
            this.available = available;
            this.vaultConnected = vaultConnected;
            this.pythonExecutable = pythonExecutable;
            this.builderScript = builderScript;
            this.timeoutMinutes = timeoutMinutes;
        }

        public boolean isAvailable() {
            return available;
        }

        public boolean isVaultConnected() {
            return vaultConnected;
        }

        public String getPythonExecutable() {
            return pythonExecutable;
        }

        public String getBuilderScript() {
            return builderScript;
        }

        public int getTimeoutMinutes() {
            return timeoutMinutes;
        }
    }
}
