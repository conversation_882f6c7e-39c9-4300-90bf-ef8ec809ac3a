package com.cicd.orchestrator.service;

import com.cicd.orchestrator.config.OrchestratorConfig;
import com.cicd.orchestrator.service.StorageServiceClient.DockerComposeResponse;
import org.apache.commons.exec.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

@Service
public class DeployerServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(DeployerServiceManager.class);

    @Autowired
    private OrchestratorConfig config;

    @Autowired
    private StorageServiceClient storageServiceClient;

    /**
     * Execute deployment for a given compose file
     */
    public CompletableFuture<Boolean> executeDeploy(DockerComposeResponse dockerCompose) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Starting deployment execution for compose ID: {}", dockerCompose.getId());

                // Status updates handled by PipelineOrchestrator
                logger.info("Starting deployment for compose ID: {}", dockerCompose.getId());

                // Create temporary compose file for deployment
                Path tempComposeFile = createTempComposeFile(dockerCompose);
                if (tempComposeFile == null) {
                    logger.error("Failed to create temporary compose file for deployment");
                    return false;
                }

                try {
                    // Execute Java deployer
                    boolean deploySuccess = executeJavaDeployer(tempComposeFile);

                    if (deploySuccess) {
                        logger.info("Deployment completed successfully for compose ID: {}", dockerCompose.getId());
                        return true;
                    } else {
                        logger.error("Deployment failed for compose ID: {}", dockerCompose.getId());
                        return false;
                    }
                } finally {
                    // Clean up temporary file
                    try {
                        Files.deleteIfExists(tempComposeFile);
                    } catch (IOException e) {
                        logger.warn("Failed to delete temporary compose file: {}", tempComposeFile, e);
                    }
                }

            } catch (Exception e) {
                logger.error("Error executing deployment for compose: " + dockerCompose.getId(), e);
                return false;
            }
        });
    }

    /**
     * Create a temporary compose file for deployment
     */
    private Path createTempComposeFile(DockerComposeResponse dockerCompose) {
        try {
            // Create temp directory if it doesn't exist
            Path tempDir = Paths.get(config.getDirectories().getTemp());
            Files.createDirectories(tempDir);

            // Create temporary compose file
            String fileName = String.format("docker-compose-%s-%d.yml",
                    dockerCompose.getCommitId(), dockerCompose.getId());
            Path tempFile = tempDir.resolve(fileName);

            // Get compose content from storage service if not available
            String composeContent = dockerCompose.getComposeContent();
            if (composeContent == null || composeContent.trim().isEmpty()) {
                logger.info("Compose content not available, fetching from storage service for ID: {}",
                        dockerCompose.getId());
                composeContent = fetchComposeContentFromStorage(dockerCompose.getId());
                if (composeContent == null) {
                    logger.error("Failed to fetch compose content from storage service for ID: {}",
                            dockerCompose.getId());
                    return null;
                }
            }

            // Write compose content to file
            Files.writeString(tempFile, composeContent);

            logger.info("Created temporary compose file: {}", tempFile);
            return tempFile;

        } catch (IOException e) {
            logger.error("Failed to create temporary compose file", e);
            return null;
        }
    }

    /**
     * Fetch compose content from storage service
     */
    private String fetchComposeContentFromStorage(Long composeId) {
        try {
            return storageServiceClient.getComposeContent(composeId);
        } catch (Exception e) {
            logger.error("Error fetching compose content for ID: " + composeId, e);
            return null;
        }
    }

    /**
     * Execute the Java deployer service
     */
    private boolean executeJavaDeployer(Path composeFile) {
        try {
            logger.info("Executing Java deployer with compose file: {}", composeFile);

            // Check if deployer JAR exists
            Path deployerJar = Paths.get(config.getDeployer().getJarPath());
            if (!Files.exists(deployerJar)) {
                logger.error("Deployer JAR not found at: {}", deployerJar);
                return false;
            }

            // Prepare command
            CommandLine cmdLine = new CommandLine("java");
            cmdLine.addArgument("-jar");
            cmdLine.addArgument(deployerJar.toString());
            cmdLine.addArgument(composeFile.toString());

            // Set up execution environment
            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0); // Success exit code

            // Set working directory to temp directory
            executor.setWorkingDirectory(new File(config.getDirectories().getTemp()));

            // Set up output streams
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
            executor.setStreamHandler(streamHandler);

            // Set timeout
            ExecuteWatchdog watchdog = new ExecuteWatchdog(config.getDeployer().getTimeoutMinutes() * 60 * 1000L);
            executor.setWatchdog(watchdog);

            // Execute command
            logger.info("Executing command: {}", cmdLine.toString());
            int exitCode = executor.execute(cmdLine);

            // Log output
            String output = outputStream.toString();
            String error = errorStream.toString();

            if (!output.isEmpty()) {
                logger.info("Deployer output: {}", output);
            }
            if (!error.isEmpty()) {
                logger.warn("Deployer error output: {}", error);
            }

            if (exitCode == 0) {
                logger.info("Java deployer completed successfully for compose file: {}", composeFile);
                return true;
            } else {
                logger.error("Java deployer failed with exit code: {} for compose file: {}", exitCode, composeFile);
                return false;
            }

        } catch (IOException e) {
            logger.error("Failed to execute Java deployer for compose file: " + composeFile, e);
            return false;
        } catch (Exception e) {
            logger.error("Unexpected error during deployment execution for compose file: " + composeFile, e);
            return false;
        }
    }

    /**
     * Execute rollback to previous deployment
     */
    public CompletableFuture<Boolean> executeRollback() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Executing rollback");

                // Check if deployer JAR exists
                Path deployerJar = Paths.get(config.getDeployer().getJarPath());
                if (!Files.exists(deployerJar)) {
                    logger.error("Deployer JAR not found at: {}", deployerJar);
                    return false;
                }

                CommandLine cmdLine = new CommandLine("java");
                cmdLine.addArgument("-jar");
                cmdLine.addArgument(deployerJar.toString());
                cmdLine.addArgument("rollback");

                DefaultExecutor executor = new DefaultExecutor();
                executor.setExitValue(0);
                executor.setWorkingDirectory(new File(config.getDirectories().getTemp()));

                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
                PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
                executor.setStreamHandler(streamHandler);

                ExecuteWatchdog watchdog = new ExecuteWatchdog(10 * 60 * 1000L); // 10 minutes
                executor.setWatchdog(watchdog);

                int exitCode = executor.execute(cmdLine);

                String output = outputStream.toString();
                String error = errorStream.toString();

                if (!output.isEmpty()) {
                    logger.info("Rollback output: {}", output);
                }
                if (!error.isEmpty()) {
                    logger.warn("Rollback error output: {}", error);
                }

                if (exitCode == 0) {
                    logger.info("Rollback completed successfully");
                    return true;
                } else {
                    logger.error("Rollback failed with exit code: {}", exitCode);
                    return false;
                }

            } catch (IOException e) {
                logger.error("Failed to execute rollback", e);
                return false;
            }
        });
    }

    /**
     * Check if deployer service is available
     */
    public boolean isDeployerAvailable() {
        try {
            Path deployerJar = Paths.get(config.getDeployer().getJarPath());
            boolean jarExists = Files.exists(deployerJar);

            if (!jarExists) {
                logger.debug("Deployer JAR not found at: {}", deployerJar);
                return false;
            }

            // Test Java availability
            CommandLine cmdLine = new CommandLine("java");
            cmdLine.addArgument("-version");

            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream);
            executor.setStreamHandler(streamHandler);

            ExecuteWatchdog watchdog = new ExecuteWatchdog(5000); // 5 seconds timeout
            executor.setWatchdog(watchdog);

            int exitCode = executor.execute(cmdLine);

            if (exitCode == 0) {
                logger.debug("Java runtime available for deployer");
                return true;
            }

            return false;

        } catch (Exception e) {
            logger.debug("Deployer availability check failed", e);
            return false;
        }
    }

    /**
     * Get deployer service status
     */
    public DeployerStatus getDeployerStatus() {
        boolean available = isDeployerAvailable();
        Path deployerJar = Paths.get(config.getDeployer().getJarPath());
        boolean jarExists = Files.exists(deployerJar);

        return new DeployerStatus(
                available,
                jarExists,
                config.getDeployer().getJarPath(),
                config.getDeployer().getTimeoutMinutes());
    }

    public static class DeployerStatus {
        private final boolean available;
        private final boolean jarExists;
        private final String jarPath;
        private final int timeoutMinutes;

        public DeployerStatus(boolean available, boolean jarExists, String jarPath, int timeoutMinutes) {
            this.available = available;
            this.jarExists = jarExists;
            this.jarPath = jarPath;
            this.timeoutMinutes = timeoutMinutes;
        }

        public boolean isAvailable() {
            return available;
        }

        public boolean isJarExists() {
            return jarExists;
        }

        public String getJarPath() {
            return jarPath;
        }

        public int getTimeoutMinutes() {
            return timeoutMinutes;
        }
    }
}
