package com.cicd.orchestrator.service;

import com.cicd.orchestrator.model.WebhookPayload;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

@Service
public class StorageServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(StorageServiceClient.class);

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public StorageServiceClient() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get the storage service base URL from environment variable or default
     */
    private String getStorageServiceUrl() {
        String url = System.getenv("ORCHESTRATOR_STORAGE_SERVICE_URL");
        return url != null ? url : "http://localhost:8080";
    }

    /**
     * Store a config build in the storage service
     */
    public Optional<ConfigBuildResponse> storeConfigBuild(WebhookPayload webhookPayload) {
        try {
            logger.info("Storing config build for commit: {}", webhookPayload.getCommit().getId());

            // Create request payload
            LocalDateTime timestamp = null;
            if (webhookPayload.getCommit().getTimestamp() != null) {
                try {
                    // Try parsing with timezone offset first
                    if (webhookPayload.getCommit().getTimestamp().contains("+") ||
                            webhookPayload.getCommit().getTimestamp().contains("Z")) {
                        timestamp = java.time.OffsetDateTime.parse(webhookPayload.getCommit().getTimestamp())
                                .toLocalDateTime();
                    } else {
                        timestamp = LocalDateTime.parse(webhookPayload.getCommit().getTimestamp());
                    }
                } catch (Exception e) {
                    logger.warn("Failed to parse timestamp, using current time: {}", e.getMessage());
                    timestamp = LocalDateTime.now();
                }
            } else {
                timestamp = LocalDateTime.now();
            }

            // Create commit info object
            ConfigBuildRequest.CommitInfo commitInfo = new ConfigBuildRequest.CommitInfo(
                    webhookPayload.getCommit().getAuthor(),
                    webhookPayload.getCommit().getModifiedFiles(),
                    webhookPayload.getCommit().getId(),
                    webhookPayload.getCommit().getMessage(),
                    webhookPayload.getCommit().getEmail(),
                    timestamp);

            ConfigBuildRequest request = new ConfigBuildRequest(
                    webhookPayload.getPusher(),
                    commitInfo,
                    webhookPayload.getRepository(),
                    webhookPayload.getBranch());

            String response = webClient.post()
                    .uri(getStorageServiceUrl() + "/config-build")
                    .header("Content-Type", "application/json")
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response != null) {
                ConfigBuildResponse configBuild = objectMapper.readValue(response, ConfigBuildResponse.class);
                logger.info("Config build stored with ID: {}", configBuild.getId());
                return Optional.of(configBuild);
            }

            return Optional.empty();

        } catch (Exception e) {
            logger.error("Error storing config build", e);
            return Optional.empty();
        }
    }

    /**
     * Get the next pending build from storage service
     */
    public Optional<ConfigBuildResponse> getNextPendingBuild() {
        try {
            String response = webClient.get()
                    .uri(getStorageServiceUrl() + "/config-build/next")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response != null && !response.trim().isEmpty()) {
                ConfigBuildResponse configBuild = objectMapper.readValue(response, ConfigBuildResponse.class);
                logger.info("Found next pending build: {}", configBuild.getId());
                return Optional.of(configBuild);
            }

            logger.debug("No pending builds found");
            return Optional.empty();

        } catch (Exception e) {
            logger.error("Error getting next pending build", e);
            return Optional.empty();
        }
    }

    /**
     * Store generated docker compose in storage service
     */
    public Optional<DockerComposeResponse> storeGeneratedCompose(Long buildConfigId, String commitId,
            String composeFilePath, String composeContent) {
        try {
            logger.info("Storing generated compose for commit: {}", commitId);

            DockerComposeRequest request = new DockerComposeRequest(
                    buildConfigId,
                    commitId,
                    composeContent);

            String response = webClient.post()
                    .uri(getStorageServiceUrl() + "/generated-docker-compose")
                    .header("Content-Type", "application/json")
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response != null) {
                DockerComposeResponse dockerCompose = objectMapper.readValue(response, DockerComposeResponse.class);
                logger.info("Generated compose stored with ID: {}", dockerCompose.getId());
                return Optional.of(dockerCompose);
            }

            return Optional.empty();

        } catch (Exception e) {
            logger.error("Error storing generated compose", e);
            return Optional.empty();
        }
    }

    /**
     * Get the next compose file for deployment
     */
    public Optional<DockerComposeResponse> getNextComposeForDeployment() {
        try {
            String response = webClient.get()
                    .uri(getStorageServiceUrl() + "/generated-docker-compose/next")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response != null && !response.trim().isEmpty()) {
                DockerComposeResponse dockerCompose = objectMapper.readValue(response, DockerComposeResponse.class);
                logger.info("Found next compose for deployment: {}", dockerCompose.getId());
                return Optional.of(dockerCompose);
            }

            logger.debug("No compose files ready for deployment");
            return Optional.empty();

        } catch (Exception e) {
            logger.error("Error getting next compose for deployment", e);
            return Optional.empty();
        }
    }

    /**
     * Update build status
     */
    public void updateBuildStatus(Long buildId, String status) {
        try {
            logger.info("Updating build {} status to: {}", buildId, status);

            Map<String, String> statusUpdate = Map.of("status", status);

            webClient.put()
                    .uri(getStorageServiceUrl() + "/config-build/" + buildId + "/status")
                    .header("Content-Type", "application/json")
                    .bodyValue(statusUpdate)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            logger.info("Build status updated successfully");

        } catch (Exception e) {
            logger.error("Error updating build status", e);
        }
    }

    /**
     * Update compose status
     */
    public void updateComposeStatus(Long composeId, String status) {
        try {
            logger.info("Updating compose {} status to: {}", composeId, status);

            Map<String, String> statusUpdate = Map.of("status", status);

            webClient.put()
                    .uri(getStorageServiceUrl() + "/generated-docker-compose/" + composeId + "/status")
                    .header("Content-Type", "application/json")
                    .bodyValue(statusUpdate)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            logger.info("Compose status updated successfully");

        } catch (Exception e) {
            logger.error("Error updating compose status", e);
        }
    }

    /**
     * Count pending builds
     */
    public long countPendingBuilds() {
        try {
            String response = webClient.get()
                    .uri(getStorageServiceUrl() + "/config-build/stats")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response != null) {
                JsonNode stats = objectMapper.readTree(response);
                return stats.path("pendingCount").asLong(0);
            }

            return 0;

        } catch (Exception e) {
            logger.error("Error counting pending builds", e);
            return 0;
        }
    }

    /**
     * Count pending deployments
     */
    public long countPendingDeployments() {
        try {
            String response = webClient.get()
                    .uri(getStorageServiceUrl() + "/generated-docker-compose/stats")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();

            if (response != null) {
                JsonNode stats = objectMapper.readTree(response);
                return stats.path("pendingCount").asLong(0);
            }

            return 0;

        } catch (Exception e) {
            logger.error("Error counting pending deployments", e);
            return 0;
        }
    }

    /**
     * Test storage service connectivity
     */
    public boolean testConnection() {
        try {
            logger.info("Testing storage service connection");

            String storageServiceUrl = System.getenv("ORCHESTRATOR_STORAGE_SERVICE_URL");
            if (storageServiceUrl == null) {
                storageServiceUrl = "http://storage-service:8080";
            }

            String response = webClient.get()
                    .uri(storageServiceUrl + "/actuator/health")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(5))
                    .block();

            boolean connected = response != null;
            logger.info("Storage service connection test: {}", connected ? "SUCCESS" : "FAILED");
            return connected;

        } catch (Exception e) {
            logger.error("Storage service connection test failed", e);
            return false;
        }
    }

    // DTOs for storage service communication
    public static class ConfigBuildRequest {
        private String pusher;
        private CommitInfo commit;
        private String repository;
        private String branch;

        public ConfigBuildRequest(String pusher, CommitInfo commit, String repository, String branch) {
            this.pusher = pusher;
            this.commit = commit;
            this.repository = repository;
            this.branch = branch;
        }

        public static class CommitInfo {
            private String author;
            private java.util.List<String> modifiedFiles;
            private String id;
            private String message;
            private String email;
            private LocalDateTime timestamp;

            // Default constructor for Jackson
            public CommitInfo() {
            }

            public CommitInfo(String author, java.util.List<String> modifiedFiles, String id, String message,
                    String email, LocalDateTime timestamp) {
                this.author = author;
                this.modifiedFiles = modifiedFiles;
                this.id = id;
                this.message = message;
                this.email = email;
                this.timestamp = timestamp;
            }

            // Getters and setters for CommitInfo
            public String getAuthor() {
                return author;
            }

            public void setAuthor(String author) {
                this.author = author;
            }

            public java.util.List<String> getModifiedFiles() {
                return modifiedFiles;
            }

            public void setModifiedFiles(java.util.List<String> modifiedFiles) {
                this.modifiedFiles = modifiedFiles;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getMessage() {
                return message;
            }

            public void setMessage(String message) {
                this.message = message;
            }

            public String getEmail() {
                return email;
            }

            public void setEmail(String email) {
                this.email = email;
            }

            public LocalDateTime getTimestamp() {
                return timestamp;
            }

            public void setTimestamp(LocalDateTime timestamp) {
                this.timestamp = timestamp;
            }
        }

        // Getters and setters
        public String getPusher() {
            return pusher;
        }

        public void setPusher(String pusher) {
            this.pusher = pusher;
        }

        public CommitInfo getCommit() {
            return commit;
        }

        public void setCommit(CommitInfo commit) {
            this.commit = commit;
        }

        public String getRepository() {
            return repository;
        }

        public void setRepository(String repository) {
            this.repository = repository;
        }

        public String getBranch() {
            return branch;
        }

        public void setBranch(String branch) {
            this.branch = branch;
        }
    }

    public static class ConfigBuildResponse {
        private Long id;
        private String pusher;
        private ConfigBuildRequest.CommitInfo commit;
        private String repository;
        private String branch;
        private String configFilePath;
        private String status;
        private String createdAt;
        private String updatedAt;

        // Getters and setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getPusher() {
            return pusher;
        }

        public void setPusher(String pusher) {
            this.pusher = pusher;
        }

        public ConfigBuildRequest.CommitInfo getCommit() {
            return commit;
        }

        public void setCommit(ConfigBuildRequest.CommitInfo commit) {
            this.commit = commit;
        }

        public String getRepository() {
            return repository;
        }

        public void setRepository(String repository) {
            this.repository = repository;
        }

        public String getBranch() {
            return branch;
        }

        public void setBranch(String branch) {
            this.branch = branch;
        }

        public String getConfigFilePath() {
            return configFilePath;
        }

        public void setConfigFilePath(String configFilePath) {
            this.configFilePath = configFilePath;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }

        public String getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(String updatedAt) {
            this.updatedAt = updatedAt;
        }

        // Helper method to get commit ID for backward compatibility
        public String getCommitId() {
            return commit != null ? commit.getId() : null;
        }
    }

    public static class DockerComposeRequest {
        private Long buildConfigId;
        private String commitId;
        private String composeContent;

        public DockerComposeRequest(Long buildConfigId, String commitId, String composeContent) {
            this.buildConfigId = buildConfigId;
            this.commitId = commitId;
            this.composeContent = composeContent;
        }

        // Getters and setters
        public Long getBuildConfigId() {
            return buildConfigId;
        }

        public void setBuildConfigId(Long buildConfigId) {
            this.buildConfigId = buildConfigId;
        }

        public String getCommitId() {
            return commitId;
        }

        public void setCommitId(String commitId) {
            this.commitId = commitId;
        }

        public String getComposeContent() {
            return composeContent;
        }

        public void setComposeContent(String composeContent) {
            this.composeContent = composeContent;
        }
    }

    public static class DockerComposeResponse {
        private Long id;
        private Long buildConfigId;
        private String commitId;
        private String status;
        private String composeContent;

        // Getters and setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getBuildConfigId() {
            return buildConfigId;
        }

        public void setBuildConfigId(Long buildConfigId) {
            this.buildConfigId = buildConfigId;
        }

        public String getCommitId() {
            return commitId;
        }

        public void setCommitId(String commitId) {
            this.commitId = commitId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getComposeContent() {
            return composeContent;
        }

        public void setComposeContent(String composeContent) {
            this.composeContent = composeContent;
        }
    }
}
