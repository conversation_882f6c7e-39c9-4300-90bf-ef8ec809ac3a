package com.cicd.orchestrator.service;

import com.cicd.orchestrator.config.OrchestratorConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.HttpClientErrorException;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Service to manage and monitor external services in the multi-service
 * container
 */
@Service
public class ExternalServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(ExternalServiceManager.class);

    @Autowired
    private OrchestratorConfig config;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    private volatile boolean storageServiceAvailable = false;
    private volatile boolean webhookServiceAvailable = false;

    @PostConstruct
    public void initialize() {
        logger.info("Initializing External Service Manager for multi-service container");

        // Start health monitoring for external services
        startHealthMonitoring();
    }

    /**
     * Start health monitoring for external services
     */
    private void startHealthMonitoring() {
        // Monitor storage service
        scheduler.scheduleWithFixedDelay(() -> {
            checkStorageServiceHealth();
        }, 10, 30, TimeUnit.SECONDS);

        // Monitor webhook service
        scheduler.scheduleWithFixedDelay(() -> {
            checkWebhookServiceHealth();
        }, 15, 30, TimeUnit.SECONDS);

        logger.info("Started health monitoring for external services");
    }

    /**
     * Check storage service health
     */
    private void checkStorageServiceHealth() {
        try {
            String storageUrl = getStorageServiceUrl();
            if (storageUrl != null) {
                String healthUrl = storageUrl + "/actuator/health";
                String response = restTemplate.getForObject(healthUrl, String.class);

                if (!storageServiceAvailable) {
                    logger.info("Storage service is now available at: {}", storageUrl);
                    storageServiceAvailable = true;
                }
            }
        } catch (ResourceAccessException e) {
            if (storageServiceAvailable) {
                logger.warn("Storage service is no longer available");
                storageServiceAvailable = false;
            }
        } catch (Exception e) {
            logger.debug("Storage service health check failed: {}", e.getMessage());
            storageServiceAvailable = false;
        }
    }

    /**
     * Check webhook service health
     */
    private void checkWebhookServiceHealth() {
        try {
            String webhookUrl = getWebhookServiceUrl();
            if (webhookUrl != null) {
                // Webhook service doesn't have /health endpoint, so we test the root endpoint
                // and expect a 405 Method Not Allowed (which means the service is running)
                try {
                    restTemplate.getForObject(webhookUrl + "/", String.class);
                } catch (HttpClientErrorException e) {
                    // 405 Method Not Allowed is expected for GET request to webhook endpoint
                    if (e.getStatusCode().value() == 405) {
                        if (!webhookServiceAvailable) {
                            logger.info("Webhook service is now available at: {}", webhookUrl);
                            webhookServiceAvailable = true;
                        }
                        return;
                    }
                    throw e;
                }

                if (!webhookServiceAvailable) {
                    logger.info("Webhook service is now available at: {}", webhookUrl);
                    webhookServiceAvailable = true;
                }
            }
        } catch (ResourceAccessException e) {
            if (webhookServiceAvailable) {
                logger.warn("Webhook service is no longer available");
                webhookServiceAvailable = false;
            }
        } catch (Exception e) {
            logger.debug("Webhook service health check failed: {}", e.getMessage());
            webhookServiceAvailable = false;
        }
    }

    /**
     * Get storage service URL from configuration
     */
    public String getStorageServiceUrl() {
        try {
            return config.getExternalServices().getStorageService().getUrl();
        } catch (Exception e) {
            logger.debug("Storage service URL not configured");
            return null;
        }
    }

    /**
     * Get webhook service URL from configuration
     */
    public String getWebhookServiceUrl() {
        try {
            return config.getExternalServices().getWebhookService().getUrl();
        } catch (Exception e) {
            logger.debug("Webhook service URL not configured");
            return null;
        }
    }

    /**
     * Check if storage service is available
     */
    public boolean isStorageServiceAvailable() {
        return storageServiceAvailable;
    }

    /**
     * Check if webhook service is available
     */
    public boolean isWebhookServiceAvailable() {
        return webhookServiceAvailable;
    }

    /**
     * Wait for storage service to be available
     */
    public CompletableFuture<Boolean> waitForStorageService(int timeoutSeconds) {
        return CompletableFuture.supplyAsync(() -> {
            int attempts = 0;
            int maxAttempts = timeoutSeconds / 2;

            while (attempts < maxAttempts && !storageServiceAvailable) {
                try {
                    Thread.sleep(2000);
                    checkStorageServiceHealth();
                    attempts++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }

            return storageServiceAvailable;
        });
    }

    /**
     * Wait for webhook service to be available
     */
    public CompletableFuture<Boolean> waitForWebhookService(int timeoutSeconds) {
        return CompletableFuture.supplyAsync(() -> {
            int attempts = 0;
            int maxAttempts = timeoutSeconds / 2;

            while (attempts < maxAttempts && !webhookServiceAvailable) {
                try {
                    Thread.sleep(2000);
                    checkWebhookServiceHealth();
                    attempts++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }

            return webhookServiceAvailable;
        });
    }

    /**
     * Get overall external services status
     */
    public ExternalServicesStatus getStatus() {
        return new ExternalServicesStatus(
                storageServiceAvailable,
                webhookServiceAvailable,
                getStorageServiceUrl(),
                getWebhookServiceUrl());
    }

    /**
     * Status class for external services
     */
    public static class ExternalServicesStatus {
        private final boolean storageServiceAvailable;
        private final boolean webhookServiceAvailable;
        private final String storageServiceUrl;
        private final String webhookServiceUrl;

        public ExternalServicesStatus(boolean storageServiceAvailable, boolean webhookServiceAvailable,
                String storageServiceUrl, String webhookServiceUrl) {
            this.storageServiceAvailable = storageServiceAvailable;
            this.webhookServiceAvailable = webhookServiceAvailable;
            this.storageServiceUrl = storageServiceUrl;
            this.webhookServiceUrl = webhookServiceUrl;
        }

        public boolean isStorageServiceAvailable() {
            return storageServiceAvailable;
        }

        public boolean isWebhookServiceAvailable() {
            return webhookServiceAvailable;
        }

        public String getStorageServiceUrl() {
            return storageServiceUrl;
        }

        public String getWebhookServiceUrl() {
            return webhookServiceUrl;
        }

        public boolean isAllServicesAvailable() {
            return storageServiceAvailable && webhookServiceAvailable;
        }
    }

    /**
     * Shutdown the service manager
     */
    public void shutdown() {
        logger.info("Shutting down External Service Manager");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
