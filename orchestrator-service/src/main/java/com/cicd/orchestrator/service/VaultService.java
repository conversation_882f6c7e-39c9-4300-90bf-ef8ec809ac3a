package com.cicd.orchestrator.service;

import com.cicd.orchestrator.config.OrchestratorConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Service
public class VaultService {

    private static final Logger logger = LoggerFactory.getLogger(VaultService.class);

    @Autowired
    private OrchestratorConfig config;

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public VaultService() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get Git hosting repository credentials
     */
    public GitCredentials getGitCredentials() {
        try {
            logger.info("Fetching Git credentials from Vault");

            String username = getSecret("git-hosting-repository-username");
            String authToken = getSecret("git-hosting-repository-authToken");

            if (username == null || authToken == null) {
                logger.error("Failed to retrieve Git credentials from Vault");
                return null;
            }

            return new GitCredentials(username, authToken);

        } catch (Exception e) {
            logger.error("Error fetching Git credentials from Vault", e);
            return null;
        }
    }

    /**
     * Get Docker registry credentials
     */
    public DockerCredentials getDockerCredentials() {
        try {
            logger.info("Fetching Docker credentials from Vault");

            String registryUrl = getSecret("registry-url");
            String username = getSecret("docker-username");
            String password = getSecret("docker-password");

            // Registry URL is required
            if (registryUrl == null) {
                logger.error("Registry URL is required but not found in Vault");
                return null;
            }

            // For local registries (localhost), credentials are optional
            if (registryUrl.contains("localhost") || registryUrl.contains("127.0.0.1")) {
                logger.info("Using local registry, Docker credentials are optional");
                username = username != null ? username : "";
                password = password != null ? password : "";
            } else {
                // For remote registries, credentials are required
                if (username == null || password == null) {
                    logger.error("Docker username and password are required for remote registry: {}", registryUrl);
                    return null;
                }
            }

            logger.info("Successfully retrieved Docker credentials from Vault for registry: {}", registryUrl);
            return new DockerCredentials(registryUrl, username, password);

        } catch (Exception e) {
            logger.error("Error fetching Docker credentials from Vault", e);
            return null;
        }
    }

    /**
     * Get webhook secret for validation
     */
    public String getWebhookSecret() {
        try {
            logger.info("Fetching webhook secret from Vault");
            return getSecret("webhook-secret");
        } catch (Exception e) {
            logger.error("Error fetching webhook secret from Vault", e);
            return null;
        }
    }

    /**
     * Get Docker Hub credentials for pulling base images
     */
    public DockerCredentials getDockerHubCredentials() {
        try {
            logger.info("Fetching Docker Hub credentials from Vault");

            String username = getSecret("dockerhub-username");
            String password = getSecret("dockerhub-password");

            // Docker Hub credentials are optional
            if (username == null || password == null) {
                logger.info("Docker Hub credentials not found in Vault, using anonymous access");
                return new DockerCredentials("docker.io", "", "");
            }

            logger.info("Successfully retrieved Docker Hub credentials from Vault");
            return new DockerCredentials("docker.io", username, password);

        } catch (Exception e) {
            logger.error("Error fetching Docker Hub credentials from Vault", e);
            return new DockerCredentials("docker.io", "", "");
        }
    }

    /**
     * Get all secrets as environment variables for builder
     */
    public Map<String, String> getBuilderEnvironmentVariables() {
        try {
            logger.info("Fetching builder environment variables from Vault");

            DockerCredentials dockerCreds = getDockerCredentials();
            if (dockerCreds == null) {
                logger.error("Failed to get Docker credentials for builder");
                return Map.of();
            }

            DockerCredentials dockerHubCreds = getDockerHubCredentials();

            Map<String, String> envVars = new HashMap<>();
            envVars.put("VAULT_URL", config.getBuilder().getVaultUrl());
            envVars.put("VAULT_API_KEY", config.getBuilder().getVaultApiKey());
            envVars.put("REGISTRY_URL", dockerCreds.getRegistryUrl());
            envVars.put("DOCKER_USERNAME", dockerCreds.getUsername());
            envVars.put("DOCKER_PASSWORD", dockerCreds.getPassword());
            envVars.put("DOCKERHUB_USERNAME", dockerHubCreds.getUsername());
            envVars.put("DOCKERHUB_PASSWORD", dockerHubCreds.getPassword());

            return envVars;

        } catch (Exception e) {
            logger.error("Error fetching builder environment variables", e);
            return Map.of();
        }
    }

    /**
     * Generic method to get a secret from Vault
     */
    private String getSecret(String secretKey) {
        try {
            String url = config.getVaultService().getBaseUrl() + "/api/secrets/" + secretKey;

            String response = webClient.get()
                    .uri(url)
                    .header("X-API-Key", config.getVaultService().getApiKey())
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(config.getVaultService().getTimeoutSeconds()))
                    .block();

            if (response == null) {
                logger.error("No response from Vault for secret: {}", secretKey);
                return null;
            }

            // Parse JSON response
            JsonNode jsonNode = objectMapper.readTree(response);

            // Handle different response formats
            if (jsonNode.has("value")) {
                return jsonNode.get("value").asText();
            } else if (jsonNode.has("data") && jsonNode.get("data").has("value")) {
                return jsonNode.get("data").get("value").asText();
            } else if (jsonNode.has("secret") && jsonNode.get("secret").has("data")) {
                // Handle vault service format: {"secret": {"data": {"username": "...",
                // "authToken": "..."}}}
                JsonNode secretData = jsonNode.get("secret").get("data");

                // Handle specific field mappings based on secret key
                if (secretKey.equals("git-hosting-repository-username") && secretData.has("username")) {
                    return secretData.get("username").asText();
                } else if (secretKey.equals("git-hosting-repository-authToken") && secretData.has("authToken")) {
                    return secretData.get("authToken").asText();
                } else if (secretKey.equals("registry-url") && secretData.has("url")) {
                    return secretData.get("url").asText();
                } else if (secretKey.equals("docker-username") && secretData.has("username")) {
                    return secretData.get("username").asText();
                } else if (secretKey.equals("docker-password") && secretData.has("password")) {
                    return secretData.get("password").asText();
                }
                // Docker Hub credentials
                else if (secretKey.equals("dockerhub-username") && secretData.has("username")) {
                    return secretData.get("username").asText();
                } else if (secretKey.equals("dockerhub-password") && secretData.has("password")) {
                    return secretData.get("password").asText();
                }
                // Fallback to "value" field
                else if (secretData.has("value")) {
                    return secretData.get("value").asText();
                }
            }

            logger.error("Unexpected response format from Vault for secret: {}", secretKey);
            logger.debug("Response content: {}", response);
            return null;

        } catch (Exception e) {
            logger.error("Error fetching secret '{}' from Vault", secretKey, e);
            return null;
        }
    }

    /**
     * Test Vault connectivity
     */
    public boolean testConnection() {
        try {
            logger.info("Testing Vault connection");

            String url = config.getVaultService().getBaseUrl() + "/health";

            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(5))
                    .block();

            boolean connected = response != null;
            logger.info("Vault connection test: {}", connected ? "SUCCESS" : "FAILED");
            return connected;

        } catch (Exception e) {
            logger.error("Vault connection test failed", e);
            return false;
        }
    }

    // Data classes for credentials
    public static class GitCredentials {
        private final String username;
        private final String authToken;

        public GitCredentials(String username, String authToken) {
            this.username = username;
            this.authToken = authToken;
        }

        public String getUsername() {
            return username;
        }

        public String getAuthToken() {
            return authToken;
        }

        public String getAuthUrl(String repositoryUrl) {
            // Convert https://github.com/user/repo.git to
            // https://username:<EMAIL>/user/repo.git
            return repositoryUrl.replace("https://", "https://" + username + ":" + authToken + "@");
        }
    }

    public static class DockerCredentials {
        private final String registryUrl;
        private final String username;
        private final String password;

        public DockerCredentials(String registryUrl, String username, String password) {
            this.registryUrl = registryUrl;
            this.username = username;
            this.password = password;
        }

        public String getRegistryUrl() {
            return registryUrl;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }
    }
}
