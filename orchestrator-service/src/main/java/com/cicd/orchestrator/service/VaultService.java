package com.cicd.orchestrator.service;

import com.cicd.orchestrator.config.OrchestratorConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

@Service
public class VaultService {

    private static final Logger logger = LoggerFactory.getLogger(VaultService.class);

    @Autowired
    private OrchestratorConfig config;

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public VaultService() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get Git hosting repository credentials
     */
    public GitCredentials getGitCredentials() {
        try {
            logger.info("Fetching Git credentials from Vault");

            String username = getSecret("git-hosting-repository-username");
            String authToken = getSecret("git-hosting-repository-authToken");

            if (username == null || authToken == null) {
                logger.error("Failed to retrieve Git credentials from Vault");
                return null;
            }

            return new GitCredentials(username, authToken);

        } catch (Exception e) {
            logger.error("Error fetching Git credentials from Vault", e);
            return null;
        }
    }

    /**
     * Get Docker registry credentials
     */
    public DockerCredentials getDockerCredentials() {
        try {
            logger.info("Fetching Docker credentials from Vault");

            String registryUrl = getSecret("registry-url");
            String username = getSecret("docker-username");
            String password = getSecret("docker-password");

            if (registryUrl == null || username == null || password == null) {
                logger.error("Failed to retrieve Docker credentials from Vault");
                return null;
            }

            return new DockerCredentials(registryUrl, username, password);

        } catch (Exception e) {
            logger.error("Error fetching Docker credentials from Vault", e);
            return null;
        }
    }

    /**
     * Get webhook secret for validation
     */
    public String getWebhookSecret() {
        try {
            logger.info("Fetching webhook secret from Vault");
            return getSecret("webhook-secret");
        } catch (Exception e) {
            logger.error("Error fetching webhook secret from Vault", e);
            return null;
        }
    }

    /**
     * Get all secrets as environment variables for builder
     */
    public Map<String, String> getBuilderEnvironmentVariables() {
        try {
            logger.info("Fetching builder environment variables from Vault");

            DockerCredentials dockerCreds = getDockerCredentials();
            if (dockerCreds == null) {
                logger.error("Failed to get Docker credentials for builder");
                return Map.of();
            }

            return Map.of(
                    "VAULT_URL", config.getBuilder().getVaultUrl(),
                    "VAULT_API_KEY", config.getBuilder().getVaultApiKey(),
                    "REGISTRY_URL", dockerCreds.getRegistryUrl(),
                    "DOCKER_USERNAME", dockerCreds.getUsername(),
                    "DOCKER_PASSWORD", dockerCreds.getPassword());

        } catch (Exception e) {
            logger.error("Error fetching builder environment variables", e);
            return Map.of();
        }
    }

    /**
     * Generic method to get a secret from Vault
     */
    private String getSecret(String secretKey) {
        try {
            String url = config.getVaultService().getBaseUrl() + "/api/secrets/" + secretKey;

            String response = webClient.get()
                    .uri(url)
                    .header("X-API-Key", config.getVaultService().getApiKey())
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(config.getVaultService().getTimeoutSeconds()))
                    .block();

            if (response == null) {
                logger.error("No response from Vault for secret: {}", secretKey);
                return null;
            }

            // Parse JSON response
            JsonNode jsonNode = objectMapper.readTree(response);

            // Handle different response formats
            if (jsonNode.has("value")) {
                return jsonNode.get("value").asText();
            } else if (jsonNode.has("data") && jsonNode.get("data").has("value")) {
                return jsonNode.get("data").get("value").asText();
            } else if (jsonNode.has("secret") && jsonNode.get("secret").has("data")) {
                // Handle vault service format: {"secret": {"data": {"username": "...",
                // "authToken": "..."}}}
                JsonNode secretData = jsonNode.get("secret").get("data");

                // For git-hosting-repository-username, look for "username" field
                if (secretKey.equals("git-hosting-repository-username") && secretData.has("username")) {
                    return secretData.get("username").asText();
                }
                // For git-hosting-repository-authToken, look for "authToken" field
                else if (secretKey.equals("git-hosting-repository-authToken") && secretData.has("authToken")) {
                    return secretData.get("authToken").asText();
                }
                // Fallback to "value" field
                else if (secretData.has("value")) {
                    return secretData.get("value").asText();
                }
            }

            logger.error("Unexpected response format from Vault for secret: {}", secretKey);
            logger.debug("Response content: {}", response);
            return null;

        } catch (Exception e) {
            logger.error("Error fetching secret '{}' from Vault", secretKey, e);
            return null;
        }
    }

    /**
     * Test Vault connectivity
     */
    public boolean testConnection() {
        try {
            logger.info("Testing Vault connection");

            String url = config.getVaultService().getBaseUrl() + "/health";

            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(5))
                    .block();

            boolean connected = response != null;
            logger.info("Vault connection test: {}", connected ? "SUCCESS" : "FAILED");
            return connected;

        } catch (Exception e) {
            logger.error("Vault connection test failed", e);
            return false;
        }
    }

    // Data classes for credentials
    public static class GitCredentials {
        private final String username;
        private final String authToken;

        public GitCredentials(String username, String authToken) {
            this.username = username;
            this.authToken = authToken;
        }

        public String getUsername() {
            return username;
        }

        public String getAuthToken() {
            return authToken;
        }

        public String getAuthUrl(String repositoryUrl) {
            // Convert https://github.com/user/repo.git to
            // https://username:<EMAIL>/user/repo.git
            return repositoryUrl.replace("https://", "https://" + username + ":" + authToken + "@");
        }
    }

    public static class DockerCredentials {
        private final String registryUrl;
        private final String username;
        private final String password;

        public DockerCredentials(String registryUrl, String username, String password) {
            this.registryUrl = registryUrl;
            this.username = username;
            this.password = password;
        }

        public String getRegistryUrl() {
            return registryUrl;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }
    }
}
