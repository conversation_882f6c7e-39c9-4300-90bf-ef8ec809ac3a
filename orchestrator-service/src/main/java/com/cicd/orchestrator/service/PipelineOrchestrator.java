package com.cicd.orchestrator.service;

import com.cicd.orchestrator.model.WebhookPayload;
import com.cicd.orchestrator.service.StorageServiceClient.ConfigBuildResponse;
import com.cicd.orchestrator.service.StorageServiceClient.DockerComposeResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class PipelineOrchestrator {

    private static final Logger logger = LoggerFactory.getLogger(PipelineOrchestrator.class);

    @Autowired
    private StorageServiceClient storageServiceClient;

    @Autowired
    private GitRepositoryManager gitRepositoryManager;

    @Autowired
    private BuilderServiceManager builderServiceManager;

    @Autowired
    private DeployerServiceManager deployerServiceManager;

    private final ObjectMapper yamlMapper;
    private final ObjectMapper jsonMapper;
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);

    public PipelineOrchestrator() {
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
        this.jsonMapper = new ObjectMapper();
    }

    /**
     * Process webhook configuration file detected by file watcher
     */
    @Async
    public CompletableFuture<Void> processWebhookConfigFile(Path configPath) {
        try {
            logger.info("Processing webhook config file: {}", configPath);

            // Parse webhook configuration
            WebhookPayload webhookPayload = parseWebhookConfig(configPath);
            if (webhookPayload == null) {
                logger.error("Failed to parse webhook config: {}", configPath);
                return CompletableFuture.completedFuture(null);
            }

            // Store webhook payload in storage service
            Optional<ConfigBuildResponse> savedConfig = storageServiceClient.storeConfigBuild(webhookPayload);
            if (savedConfig.isPresent()) {
                logger.info("Stored config build with ID: {} for commit: {}", savedConfig.get().getId(),
                        savedConfig.get().getCommitId());
            } else {
                logger.error("Failed to store config build for commit: {}", webhookPayload.getCommit().getId());
                return CompletableFuture.completedFuture(null);
            }

            // Start sequential processing if not already running
            startSequentialProcessing();

        } catch (Exception e) {
            logger.error("Error processing webhook config: " + configPath, e);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Start sequential processing of pending builds and deployments
     */
    public void startSequentialProcessing() {
        if (isProcessing.compareAndSet(false, true)) {
            logger.info("Starting sequential pipeline processing");

            CompletableFuture.runAsync(() -> {
                try {
                    processSequentially();
                } finally {
                    isProcessing.set(false);
                    logger.info("Sequential pipeline processing completed");
                }
            });
        } else {
            logger.debug("Sequential processing already running");
        }
    }

    /**
     * Process builds and deployments sequentially
     */
    private void processSequentially() {
        try {
            // Process pending builds first
            processPendingBuilds();

            // Then process pending deployments
            processPendingDeployments();

        } catch (Exception e) {
            logger.error("Error in sequential processing", e);
        }
    }

    /**
     * Process all pending builds sequentially
     */
    private void processPendingBuilds() {
        logger.info("Processing pending builds");

        Optional<ConfigBuildResponse> nextBuild;
        while ((nextBuild = storageServiceClient.getNextPendingBuild()).isPresent()) {
            ConfigBuildResponse build = nextBuild.get();
            logger.info("Processing build: {} for commit: {}", build.getId(), build.getCommitId());

            try {
                // Update build status to IN_PROGRESS
                storageServiceClient.updateBuildStatus(build.getId(), "IN_PROGRESS");

                // Clone repository
                String repositoryUrl = gitRepositoryManager.extractRepositoryUrl(build.getRepository());
                String projectPath = gitRepositoryManager.cloneRepository(
                        repositoryUrl,
                        build.getCommitId(),
                        build.getBranch());

                if (projectPath == null) {
                    logger.error("Failed to clone repository for build: {}", build.getId());
                    // Note: Storage service doesn't support FAILED status, keeping as IN_PROGRESS
                    // for now
                    // storageServiceClient.updateBuildStatus(build.getId(), "FAILED");
                    continue;
                }

                // Execute build
                boolean buildSuccess = builderServiceManager.executeBuild(projectPath, build.getCommitId()).get();

                if (buildSuccess) {
                    logger.info("Build completed successfully: {}", build.getId());
                    storageServiceClient.updateBuildStatus(build.getId(), "BUILT");

                    // Process generated compose files
                    processGeneratedComposeFiles(projectPath, build);
                } else {
                    logger.error("Build failed: {}", build.getId());
                    // Note: Storage service doesn't support FAILED status, keeping as IN_PROGRESS
                    // for now
                    // storageServiceClient.updateBuildStatus(build.getId(), "FAILED");
                }

            } catch (Exception e) {
                logger.error("Error processing build: " + build.getId(), e);
                // Note: Storage service doesn't support FAILED status, keeping as IN_PROGRESS
                // for now
                // storageServiceClient.updateBuildStatus(build.getId(), "FAILED");
            }
        }

        logger.info("No more pending builds to process");
    }

    /**
     * Process generated compose files from a successful build
     */
    private void processGeneratedComposeFiles(String projectPath, ConfigBuildResponse build) {
        try {
            Path generatedComposeDir = Paths.get(projectPath, "generatedComposeFiles");

            if (!Files.exists(generatedComposeDir)) {
                logger.warn("Generated compose files directory does not exist: {}", generatedComposeDir);
                return;
            }

            // Find all compose files
            List<Path> composeFiles = Files.list(generatedComposeDir)
                    .filter(path -> path.getFileName().toString().endsWith(".yml") ||
                            path.getFileName().toString().endsWith(".yaml"))
                    .toList();

            for (Path composeFile : composeFiles) {
                try {
                    String composeContent = Files.readString(composeFile);

                    Optional<DockerComposeResponse> dockerCompose = storageServiceClient.storeGeneratedCompose(
                            build.getId(),
                            build.getCommitId(),
                            composeFile.toString(),
                            composeContent);

                    if (dockerCompose.isPresent()) {
                        logger.info("Stored generated compose file: {} for build: {}", composeFile.getFileName(),
                                build.getId());
                    } else {
                        logger.error("Failed to store compose file: {} for build: {}", composeFile.getFileName(),
                                build.getId());
                    }

                } catch (IOException e) {
                    logger.error("Failed to read compose file: " + composeFile, e);
                }
            }

        } catch (IOException e) {
            logger.error("Error processing generated compose files for build: " + build.getId(), e);
        }
    }

    /**
     * Process all pending deployments sequentially
     */
    private void processPendingDeployments() {
        logger.info("Processing pending deployments");

        Optional<DockerComposeResponse> nextCompose;
        while ((nextCompose = storageServiceClient.getNextComposeForDeployment()).isPresent()) {
            DockerComposeResponse compose = nextCompose.get();
            logger.info("Processing deployment: {} for commit: {}", compose.getId(), compose.getCommitId());

            try {
                // Execute deployment
                // Update status to IN_PROGRESS
                storageServiceClient.updateComposeStatus(compose.getId(), "IN_PROGRESS");

                boolean deploySuccess = deployerServiceManager.executeDeploy(compose).get();

                if (deploySuccess) {
                    logger.info("Deployment completed successfully: {}", compose.getId());
                    storageServiceClient.updateComposeStatus(compose.getId(), "DEPLOYED");
                } else {
                    logger.error("Deployment failed: {}", compose.getId());
                    storageServiceClient.updateComposeStatus(compose.getId(), "FAILED");
                }

            } catch (Exception e) {
                logger.error("Error processing deployment: " + compose.getId(), e);
                storageServiceClient.updateComposeStatus(compose.getId(), "FAILED");
            }
        }

        logger.info("No more pending deployments to process");
    }

    /**
     * Parse webhook configuration from file
     */
    private WebhookPayload parseWebhookConfig(Path configPath) {
        try {
            String fileName = configPath.getFileName().toString().toLowerCase();

            if (fileName.endsWith(".json")) {
                return jsonMapper.readValue(configPath.toFile(), WebhookPayload.class);
            } else if (fileName.endsWith(".yaml") || fileName.endsWith(".yml")) {
                return yamlMapper.readValue(configPath.toFile(), WebhookPayload.class);
            } else {
                logger.error("Unsupported config file format: {}", fileName);
                return null;
            }

        } catch (IOException e) {
            logger.error("Failed to parse webhook config: " + configPath, e);
            return null;
        }
    }

    // ConfigBuild conversion no longer needed - using storage service client
    // directly

    /**
     * Manual trigger for processing (for testing or manual intervention)
     */
    public void triggerManualProcessing() {
        logger.info("Manual processing triggered");
        startSequentialProcessing();
    }

    /**
     * Get pipeline status
     */
    public PipelineStatus getPipelineStatus() {
        long pendingBuilds = storageServiceClient.countPendingBuilds();
        long pendingDeployments = storageServiceClient.countPendingDeployments();

        return new PipelineStatus(
                isProcessing.get(),
                pendingBuilds,
                pendingDeployments,
                builderServiceManager.isBuilderAvailable(),
                deployerServiceManager.isDeployerAvailable());
    }

    public static class PipelineStatus {
        private final boolean processing;
        private final long pendingBuilds;
        private final long pendingDeployments;
        private final boolean builderAvailable;
        private final boolean deployerAvailable;

        public PipelineStatus(boolean processing, long pendingBuilds, long pendingDeployments,
                boolean builderAvailable, boolean deployerAvailable) {
            this.processing = processing;
            this.pendingBuilds = pendingBuilds;
            this.pendingDeployments = pendingDeployments;
            this.builderAvailable = builderAvailable;
            this.deployerAvailable = deployerAvailable;
        }

        public boolean isProcessing() {
            return processing;
        }

        public long getPendingBuilds() {
            return pendingBuilds;
        }

        public long getPendingDeployments() {
            return pendingDeployments;
        }

        public boolean isBuilderAvailable() {
            return builderAvailable;
        }

        public boolean isDeployerAvailable() {
            return deployerAvailable;
        }
    }
}
