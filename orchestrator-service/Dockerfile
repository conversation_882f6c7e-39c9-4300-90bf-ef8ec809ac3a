# Multi-stage build for the orchestrator service
FROM maven:3-openjdk-17 AS build

# Set working directory
WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code and build
COPY src ./src
RUN mvn clean package -DskipTests

# Runtime stage
FROM openjdk:17-jdk-slim

# Install required tools and Python
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    wget \
    unzip \
    git \
    tree  \
    && rm -rf /var/lib/apt/lists/*

# Create Python virtual environment and install dependencies
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies for builder service
RUN pip install --no-cache-dir \
    pyyaml \
    docker \
    requests \
    python-dotenv \
    gitpython \
    jinja2 \
    cryptography


# Create application user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create comprehensive application directories for multi-service architecture
RUN mkdir -p /orchestrator/webhook/ci \
    /orchestrator/projects \
    /orchestrator/storage/database \
    /orchestrator/storage/config-builds \
    /orchestrator/storage/docker-compose \
    /orchestrator/storage/projects \
    /orchestrator/storage/builds \
    /orchestrator/storage/compose-files \
    /orchestrator/logs \
    /orchestrator/temp \
    /orchestrator/builder/workspace \
    /orchestrator/deployer/temp \
    /app/services \
    /app/scripts \
    && chown -R appuser:appuser /orchestrator /app

# Copy orchestrator JAR (main service with integrated webhook, builder, deployer, storage)
COPY --from=build /app/target/orchestrator-service-*.jar /app/orchestrator.jar

# Copy storage service JAR (integrated into orchestrator container)
COPY --chown=appuser:appuser services/storage-service-*.jar /app/services/storage-service.jar

# Copy webhook-listener JAR (your battle-tested webhook service)
COPY --chown=appuser:appuser services/webhook-listener.jar /app/services/webhook-listener.jar

# Note: All services (webhook-listener, builder, deployer, storage) are now integrated into the orchestrator container

# All builder and deployer functionality is integrated into the orchestrator JAR

# Create startup script to run webhook-listener, storage service, and orchestrator
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Create required directories\n\
mkdir -p /app/data\n\
mkdir -p /app/storage\n\
mkdir -p /orchestrator/webhook/ci\n\
mkdir -p /tmp/logs\n\
\n\
echo "Starting Webhook Listener Service on port 8081..."\n\
SERVER_PORT=8081 LOGGING_LEVEL_ROOT=INFO java -Dlogging.file.name=/dev/null -Dlogging.pattern.console="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n" -jar /app/services/webhook-listener.jar > /tmp/logs/webhook.log 2>&1 &\n\
WEBHOOK_PID=$!\n\
echo "Webhook Listener started with PID: $WEBHOOK_PID"\n\
\n\
echo "Starting Storage Service on port 8080..."\n\
SERVER_PORT=8080 java -jar /app/services/storage-service.jar &\n\
STORAGE_PID=$!\n\
echo "Storage Service started with PID: $STORAGE_PID"\n\
\n\
echo "Waiting for services to be ready..."\n\
sleep 15\n\
\n\
echo "Starting Orchestrator Service on port 8082..."\n\
SERVER_PORT=8082 java -jar /app/orchestrator.jar &\n\
ORCHESTRATOR_PID=$!\n\
echo "Orchestrator Service started with PID: $ORCHESTRATOR_PID"\n\
\n\
# Wait for all processes\n\
wait $WEBHOOK_PID $STORAGE_PID $ORCHESTRATOR_PID\n\
' > /app/start-services.sh && chmod +x /app/start-services.sh

# Switch to application user
USER appuser

# Expose webhook-listener, orchestrator, and storage service ports
EXPOSE 8081 8082 8080

# Set working directory
WORKDIR /orchestrator

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/orchestrator/health || exit 1

# Environment variables
ENV SPRING_PROFILES_ACTIVE=docker
ENV JAVA_OPTS="-Xmx2g -Xms1g"

# Start both storage service and orchestrator service
ENTRYPOINT ["/app/start-services.sh"]