package com.bgdeployer.service;

import com.bgdeployer.config.Configuration;
import com.bgdeployer.exception.HealthCheckException;
import com.bgdeployer.util.DockerUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Health checker for services in the blue-green environment using <PERSON><PERSON>'s
 * native health check mechanism.
 * Responsible for verifying the health of services before switching traffic.
 */
public class HealthChecker {
    private static final Logger logger = LoggerFactory.getLogger(HealthChecker.class);

    private final Configuration config;

    public HealthChecker(Configuration config) {
        this.config = config;
    }

    /**
     * Check the health of all services in the specified color environment.
     * 
     * @param color The color of the environment to check
     * @return A map of service names to health check results
     */
    public Map<String, Boolean> checkHealth(String color) throws HealthCheckException {
        return checkHealth(color, null);
    }

    /**
     * Check the health of all services in the specified color environment and
     * update the provided metadata.
     * Uses Docker's native health check mechanism instead of HTTP endpoints.
     * 
     * @param color    The color of the environment to check
     * @param metadata The deployment metadata to update with health check
     *                 information, or null if not needed
     * @return A map of service names to health check results
     */
    public Map<String, Boolean> checkHealth(String color, com.bgdeployer.model.DeploymentMetadata metadata)
            throws HealthCheckException {
        Map<String, Boolean> results = new HashMap<>();
        List<String> healthCheckInfo = new ArrayList<>();
        String appName = config.getApplicationName() != null ? config.getApplicationName() : "myapp";

        // If in mock mode, return simulated successful health checks
        if (DockerUtils.isMockMode()) {
            logger.info("[MOCK] Performing health checks in mock mode");

            for (Configuration.ServiceConfig service : config.getServices()) {
                String serviceName = service.getName();
                // We still store this for reference, though we're not actually calling these
                // endpoints
                String healthCheckEndpoint = "http://" + serviceName + "-" + color + "-app:" + service.getPort()
                        + "/health";

                logger.info("[MOCK] Health check passed for service {}-{}", serviceName, color);
                results.put(serviceName, true);
                healthCheckInfo.add(healthCheckEndpoint);
            }

            // Update metadata if provided
            if (metadata != null) {
                metadata.setHealthCheckResults(results);
                metadata.setHealthChecks(healthCheckInfo);
            }

            logger.info("[MOCK] All health checks passed successfully");
            return results;
        }

        // Normal health check process for real Docker environments using Docker's
        // native healthcheck
        Duration timeout = config.getHealthCheck().getTimeout();
        Duration interval = config.getHealthCheck().getInterval();

        for (Configuration.ServiceConfig service : config.getServices()) {
            String serviceName = service.getName();

            // In modern Docker Compose, container names follow the pattern:
            // project_name-service_name-color-instance_number
            // We need to find all containers with this pattern and check each one
            String projectName = String.format("%s_%s", appName, color);
            String containerNamePattern = projectName + "-" + serviceName + "-" + color;

            logger.info("Finding containers for service {} with pattern: {}", serviceName, containerNamePattern);
            List<String> containerIds = findContainersByPattern(containerNamePattern);

            if (containerIds.isEmpty()) {
                logger.error("No containers found for service {} with pattern {}", serviceName, containerNamePattern);
                results.put(serviceName, false);
                throw new HealthCheckException("No containers found for service " + serviceName + "-" + color);
            }

            // For each found container, check health
            boolean allHealthy = true;
            for (String containerId : containerIds) {
                String containerName = getContainerNameById(containerId);

                // Store info about what we're checking
                String healthCheckInfo1 = String.format("Docker health check for container: %s (ID: %s)",
                        containerName, containerId);
                healthCheckInfo.add(healthCheckInfo1);

                logger.info("Starting Docker health check for container {} with timeout {} seconds",
                        containerName, timeout.getSeconds());

                boolean healthy = awaitDockerHealthy(containerId, timeout, interval);

                if (!healthy) {
                    logger.error("Docker health check failed for container {} of service {}-{}",
                            containerName, serviceName, color);
                    allHealthy = false;
                    break;
                }
            }

            results.put(serviceName, allHealthy);

            if (!allHealthy) {
                throw new HealthCheckException("Health check failed for service " + serviceName + "-" + color);
            }
        }

        // Update metadata if provided
        if (metadata != null) {
            metadata.setHealthCheckResults(results);
            metadata.setHealthChecks(healthCheckInfo);
        }

        return results;
    }

    /**
     * Wait for a Docker container to become healthy using Docker's native health
     * check.
     * 
     * @param containerId The ID or name of the Docker container to check
     * @param timeout     The maximum time to wait
     * @param interval    How often to check
     * @return true if the container is healthy, false otherwise
     */
    public boolean awaitDockerHealthy(String containerId, Duration timeout, Duration interval) {
        String containerDisplay = containerId;
        try {
            // If we have an ID, also try to get the name for better logging
            String containerName = getContainerNameById(containerId);
            if (containerName != null && !containerName.equals(containerId)) {
                containerDisplay = containerName + " (" + containerId + ")";
            }
        } catch (Exception e) {
            // If we can't get the name, just use the ID
            logger.debug("Could not get container name for {}, using ID for display", containerId);
        }

        logger.info("Waiting for container {} to be healthy with timeout {} seconds",
                containerDisplay, timeout.getSeconds());

        Instant startTime = Instant.now();
        Instant endTime = startTime.plus(timeout);

        while (Instant.now().isBefore(endTime)) {
            String healthStatus = getDockerHealthStatus(containerId);

            if ("healthy".equalsIgnoreCase(healthStatus)) {
                Duration elapsed = Duration.between(startTime, Instant.now());
                logger.info("Container {} is healthy after {} seconds", containerDisplay, elapsed.getSeconds());
                return true;
            } else if ("unhealthy".equalsIgnoreCase(healthStatus)) {
                // If explicitly unhealthy, no need to wait further
                logger.error("Container {} is unhealthy", containerDisplay);
                return false;
            } else if (healthStatus == null) {
                // Container might not exist or might not have a health check defined
                logger.warn("Could not determine health status for container {}", containerDisplay);

                // Check if container exists and is running
                if (isContainerRunning(containerId)) {
                    logger.info("Container {} is running but health check is not available; assuming healthy",
                            containerDisplay);
                    return true;
                } else {
                    logger.error("Container {} is not running", containerDisplay);
                    return false;
                }
            }

            logger.debug("Container {} health status: {}, checking again in {} seconds",
                    containerDisplay, healthStatus, interval.getSeconds());

            try {
                TimeUnit.MILLISECONDS.sleep(interval.toMillis());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("Health check interrupted for {}", containerDisplay);
                return false;
            }
        }

        Duration elapsed = Duration.between(startTime, Instant.now());
        logger.error("Health check timed out for {} after {} seconds", containerDisplay, elapsed.getSeconds());
        return false;
    }

    /**
     * Get the health status of a Docker container.
     * 
     * @param containerName The name of the Docker container to check
     * @return The health status as a string (healthy, unhealthy, starting, etc.) or
     *         null if not available
     */
    private String getDockerHealthStatus(String containerName) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "docker", "inspect",
                    "--format", "{{if .State.Health}}{{.State.Health.Status}}{{else}}{{.State.Status}}{{end}}",
                    containerName);

            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String status = reader.readLine();
                return status.trim();
            } else {
                logger.warn("Failed to get health status for container {}, exit code: {}", containerName, exitCode);
                return null;
            }
        } catch (IOException | InterruptedException e) {
            logger.error("Error checking Docker health for {}: {}", containerName, e.getMessage());
            return null;
        }
    }

    /**
     * Check if a Docker container is running.
     * 
     * @param containerName The name or ID of the Docker container to check
     * @return true if the container is running, false otherwise
     */
    private boolean isContainerRunning(String containerName) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "docker", "inspect",
                    "--format", "{{.State.Running}}",
                    containerName);

            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String status = reader.readLine();
                return "true".equalsIgnoreCase(status.trim());
            } else {
                logger.warn("Failed to check if container {} is running, exit code: {}", containerName, exitCode);
                return false;
            }
        } catch (IOException | InterruptedException e) {
            logger.error("Error checking if container {} is running: {}", containerName, e.getMessage());
            return false;
        }
    }

    /**
     * Find container IDs for containers matching a name pattern.
     * This is needed for Docker Compose v3 where container names follow the
     * pattern:
     * project_name_service_name_instance_number
     * 
     * @param namePattern The pattern to search for (e.g. "myapp_blue_web")
     * @return List of container IDs matching the pattern
     */
    private List<String> findContainersByPattern(String namePattern) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "docker", "ps", "-a", "-q", "--filter", "name=" + namePattern);

            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                List<String> containerIds = new ArrayList<>();
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        containerIds.add(line.trim());
                    }
                }

                if (containerIds.isEmpty()) {
                    logger.warn("No containers found matching pattern: {}", namePattern);
                } else {
                    logger.info("Found {} containers matching pattern: {}", containerIds.size(), namePattern);
                }

                return containerIds;
            } else {
                logger.error("Failed to find containers with pattern {}, exit code: {}", namePattern, exitCode);
                return new ArrayList<>();
            }
        } catch (IOException | InterruptedException e) {
            logger.error("Error finding containers with pattern {}: {}", namePattern, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Get the container name from its ID.
     * 
     * @param containerId The container ID
     * @return The container name or the ID if the name cannot be retrieved
     */
    private String getContainerNameById(String containerId) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "docker", "inspect",
                    "--format", "{{.Name}}",
                    containerId);

            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String name = reader.readLine();
                // Docker prepends the name with a slash, remove it
                if (name != null && name.startsWith("/")) {
                    name = name.substring(1);
                }
                return name;
            } else {
                logger.warn("Failed to get name for container ID {}, exit code: {}", containerId, exitCode);
                return containerId; // Return the ID if we can't get the name
            }
        } catch (IOException | InterruptedException e) {
            logger.error("Error getting name for container ID {}: {}", containerId, e.getMessage());
            return containerId; // Return the ID if we can't get the name
        }
    }
}
