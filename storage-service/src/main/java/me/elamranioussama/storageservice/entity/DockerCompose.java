package me.elamranioussama.storageservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

@Entity
@Table(name = "docker_compose")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DockerCompose {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(name = "build_config_id", nullable = false)
    private Long buildConfigId;

    @Column(name = "compose_file_path", nullable = false)
    private String composeFilePath;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ComposeStatus status = ComposeStatus.GENERATED;

    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    public enum ComposeStatus {
        GENERATED,
        IN_PROGRESS,
        DEPLOYED,
        FAILED
    }
}
